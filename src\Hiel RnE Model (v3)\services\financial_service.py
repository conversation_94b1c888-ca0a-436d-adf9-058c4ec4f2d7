"""
Financial Model Service
=======================

Service for financial modeling calculations and analysis.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from typing import Dict, Any, Optional, Callable
import pandas as pd
import numpy as np
import logging
from datetime import datetime

from core.enhanced_financial_model import (
    EnhancedAssumptions, build_enhanced_cashflow, compute_enhanced_kpis,
    build_enhanced_sensitivity, monte_carlo_simulation, generate_monte_carlo_statistics,
    run_enhanced_scenarios
)
from ..models.project_assumptions import EnhancedProjectAssumptions


class FinancialModelService:
    """Service for financial modeling operations."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._current_results: Optional[Dict[str, Any]] = None
        self._sensitivity_results: Optional[pd.DataFrame] = None
        self._monte_carlo_results: Optional[Dict[str, Any]] = None
        self._scenario_results: Optional[Dict[str, Any]] = None
    
    def run_financial_model(self, 
                          assumptions: EnhancedProjectAssumptions,
                          progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """Run the complete financial model."""
        try:
            if progress_callback:
                progress_callback(10, "Validating assumptions...")
            
            # Validate assumptions
            validation_errors = assumptions.validate_all()
            if validation_errors:
                raise ValueError(f"Validation errors: {validation_errors}")
            
            if progress_callback:
                progress_callback(30, "Building cashflow model...")
            
            # Convert to core assumptions format
            core_assumptions = self._convert_to_core_assumptions(assumptions)
            
            # Build cashflow
            cashflow = build_enhanced_cashflow(core_assumptions)
            
            if progress_callback:
                progress_callback(60, "Computing KPIs...")
            
            # Compute KPIs
            kpis = compute_enhanced_kpis(cashflow, core_assumptions)
            
            if progress_callback:
                progress_callback(90, "Finalizing results...")
            
            # Store results
            self._current_results = {
                'cashflow': cashflow,
                'kpis': kpis,
                'assumptions': assumptions.to_dict(),
                'execution_time': datetime.now().isoformat()
            }
            
            if progress_callback:
                progress_callback(100, "Model completed successfully")
            
            self.logger.info("Financial model executed successfully")
            return self._current_results
            
        except Exception as e:
            self.logger.error(f"Error running financial model: {str(e)}")
            raise
    
    def run_sensitivity_analysis(self,
                               assumptions: EnhancedProjectAssumptions,
                               variables: Optional[list] = None,
                               progress_callback: Optional[Callable[[float, str], None]] = None) -> pd.DataFrame:
        """Run sensitivity analysis."""
        try:
            if variables is None:
                variables = ['production_mwh_year1', 'ppa_price_eur_kwh', 'capex_meur', 'discount_rate']
            
            if progress_callback:
                progress_callback(20, "Setting up sensitivity analysis...")
            
            core_assumptions = self._convert_to_core_assumptions(assumptions)
            
            if progress_callback:
                progress_callback(50, "Running sensitivity calculations...")
            
            self._sensitivity_results = build_enhanced_sensitivity(core_assumptions, variables)
            
            if progress_callback:
                progress_callback(100, "Sensitivity analysis completed")
            
            self.logger.info("Sensitivity analysis completed successfully")
            return self._sensitivity_results
            
        except Exception as e:
            self.logger.error(f"Error running sensitivity analysis: {str(e)}")
            raise
    
    def run_monte_carlo_simulation(self,
                                 assumptions: EnhancedProjectAssumptions,
                                 n_simulations: int = 1000,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """Run Monte Carlo simulation."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up Monte Carlo simulation...")
            
            core_assumptions = self._convert_to_core_assumptions(assumptions)
            
            if progress_callback:
                progress_callback(30, f"Running {n_simulations} simulations...")
            
            # Run simulation with progress tracking
            mc_results = monte_carlo_simulation(core_assumptions, n_simulations)
            
            if progress_callback:
                progress_callback(80, "Generating statistics...")
            
            # Generate statistics
            mc_stats = generate_monte_carlo_statistics(mc_results)
            
            self._monte_carlo_results = {
                'results': mc_results,
                'statistics': mc_stats,
                'n_simulations': n_simulations
            }
            
            if progress_callback:
                progress_callback(100, "Monte Carlo simulation completed")
            
            self.logger.info(f"Monte Carlo simulation completed with {n_simulations} simulations")
            return self._monte_carlo_results
            
        except Exception as e:
            self.logger.error(f"Error running Monte Carlo simulation: {str(e)}")
            raise
    
    def run_scenario_analysis(self,
                            assumptions: EnhancedProjectAssumptions,
                            progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """Run scenario analysis."""
        try:
            if progress_callback:
                progress_callback(20, "Setting up scenarios...")
            
            # Run enhanced scenarios
            self._scenario_results = run_enhanced_scenarios()
            
            if progress_callback:
                progress_callback(100, "Scenario analysis completed")
            
            self.logger.info("Scenario analysis completed successfully")
            return self._scenario_results
            
        except Exception as e:
            self.logger.error(f"Error running scenario analysis: {str(e)}")
            raise
    
    def _convert_to_core_assumptions(self, assumptions: EnhancedProjectAssumptions) -> EnhancedAssumptions:
        """Convert UI assumptions to core assumptions format."""
        # Create dictionary with all required fields
        assumptions_dict = assumptions.to_dict()

        # Remove UI-specific fields
        ui_fields = ['is_validated', 'validation_errors', 'last_modified']
        for field in ui_fields:
            assumptions_dict.pop(field, None)

        # Map parameter names from UI model to core model
        if 'project_life_years' in assumptions_dict:
            assumptions_dict['years'] = assumptions_dict.pop('project_life_years')

        # Filter to only include valid parameters for EnhancedAssumptions
        valid_params = {
            k: v for k, v in assumptions_dict.items()
            if k in EnhancedAssumptions.__dataclass_fields__
        }

        return EnhancedAssumptions(**valid_params)
    
    def get_current_results(self) -> Optional[Dict[str, Any]]:
        """Get current financial model results."""
        return self._current_results
    
    def get_sensitivity_results(self) -> Optional[pd.DataFrame]:
        """Get sensitivity analysis results."""
        return self._sensitivity_results
    
    def get_monte_carlo_results(self) -> Optional[Dict[str, Any]]:
        """Get Monte Carlo simulation results."""
        return self._monte_carlo_results
    
    def get_scenario_results(self) -> Optional[Dict[str, Any]]:
        """Get scenario analysis results."""
        return self._scenario_results
    
    def has_results(self) -> bool:
        """Check if financial model has been run."""
        return self._current_results is not None
    
    def clear_results(self):
        """Clear all cached results."""
        self._current_results = None
        self._sensitivity_results = None
        self._monte_carlo_results = None
        self._scenario_results = None
        self.logger.info("All results cleared")
    
    def get_kpi_summary(self) -> Optional[Dict[str, Any]]:
        """Get KPI summary for display."""
        if not self._current_results:
            return None
        
        kpis = self._current_results.get('kpis', {})
        return {
            'irr_project': kpis.get('IRR_project', 0),
            'irr_equity': kpis.get('IRR_equity', 0),
            'npv_project': kpis.get('NPV_project', 0),
            'npv_equity': kpis.get('NPV_equity', 0),
            'lcoe': kpis.get('LCOE_eur_kwh', 0),
            'min_dscr': kpis.get('Min_DSCR', 0),
            'avg_dscr': kpis.get('Avg_DSCR', 0),
            'payback_years': kpis.get('Payback_years', 0),
            'grant_percentage': kpis.get('Grant_percentage', 0)
        }
    
    def get_cashflow_summary(self) -> Optional[Dict[str, Any]]:
        """Get cashflow summary for display."""
        if not self._current_results:
            return None
        
        cashflow = self._current_results.get('cashflow')
        if cashflow is None:
            return None
        
        if isinstance(cashflow, dict):
            df = pd.DataFrame(cashflow)
        else:
            df = cashflow
        
        return {
            'total_revenue': df['Revenue'].sum(),
            'total_opex': abs(df['OPEX'].sum()),
            'total_capex': abs(df['Capex'].sum()),
            'total_grants': df['Grants'].sum(),
            'project_life': len(df) - 1,  # Excluding year 0
            'first_year_cf': df.loc[1, 'Equity_CF'] if 1 in df.index else 0,
            'last_year_cf': df.iloc[-1]['Equity_CF'] if len(df) > 0 else 0
        }
