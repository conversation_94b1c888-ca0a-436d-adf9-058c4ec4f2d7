"""
Dashboard View
==============

Enhanced dashboard view with comprehensive financial analysis visualization.
"""

import flet as ft
from typing import Dict, Any, Optional

from .base_view import BaseView
from ..components.charts.kpi_charts import KPICharts
from ..components.charts.cashflow_charts import CashflowCharts
from ..components.widgets.kpi_card import KPICard


class DashboardView(BaseView):
    """Enhanced dashboard view for financial analysis."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.financial_results: Optional[Dict[str, Any]] = None
        self.kpi_charts = KPICharts()
        self.cashflow_charts = CashflowCharts()
    
    def build_content(self) -> ft.Control:
        """Build the dashboard view content."""
        
        if not self.financial_results:
            return self.create_empty_state(
                "No Financial Results",
                "Run the financial model first to see the dashboard",
                "Go to Project Setup",
                lambda: self.navigate_to("project_setup")
            )
        
        # Header
        header = self.create_section_header(
            "Financial Analysis Dashboard",
            "Comprehensive overview of project financial performance"
        )
        
        # KPI Summary Cards
        kpi_summary = self._create_kpi_summary()
        
        # Financial Summary
        financial_summary = self._create_financial_summary()
        
        # Charts Grid
        charts_grid = self._create_charts_grid()
        
        # Grant Analysis
        grant_analysis = self._create_grant_analysis()
        
        # Risk Analysis
        risk_analysis = self._create_risk_analysis()
        
        return ft.Column([
            header,
            kpi_summary,
            financial_summary,
            charts_grid,
            grant_analysis,
            risk_analysis
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_kpi_summary(self) -> ft.Container:
        """Create KPI summary cards."""
        if not self.financial_results:
            return ft.Container()
        
        kpis = self.financial_results.get('kpis', {})
        
        kpi_cards = ft.Row([
            KPICard(
                title="Project IRR",
                value=f"{kpis.get('IRR_project', 0):.1%}",
                color=ft.Colors.GREEN,
                icon=ft.Icons.TRENDING_UP,
                target_value=12.0,
                current_value=kpis.get('IRR_project', 0) * 100
            ).build(),
            
            KPICard(
                title="Equity IRR",
                value=f"{kpis.get('IRR_equity', 0):.1%}",
                color=ft.Colors.BLUE,
                icon=ft.Icons.ACCOUNT_BALANCE,
                target_value=15.0,
                current_value=kpis.get('IRR_equity', 0) * 100
            ).build(),
            
            KPICard(
                title="NPV Project",
                value=self.format_currency(kpis.get('NPV_project', 0)),
                color=ft.Colors.PURPLE,
                icon=ft.Icons.MONETIZATION_ON
            ).build(),
            
            KPICard(
                title="LCOE",
                value=f"{kpis.get('LCOE_eur_kwh', 0):.3f} €/kWh",
                color=ft.Colors.ORANGE,
                icon=ft.Icons.ELECTRIC_BOLT,
                target_value=0.045,
                current_value=kpis.get('LCOE_eur_kwh', 0),
                lower_is_better=True
            ).build(),
            
            KPICard(
                title="Min DSCR",
                value=f"{kpis.get('Min_DSCR', 0):.2f}",
                color=ft.Colors.TEAL,
                icon=ft.Icons.SECURITY,
                target_value=1.25,
                current_value=kpis.get('Min_DSCR', 0)
            ).build()
        ], alignment=ft.MainAxisAlignment.SPACE_AROUND, wrap=True)
        
        return ft.Container(
            content=kpi_cards,
            padding=20,
            bgcolor=ft.Colors.GREY_50,
            border_radius=10
        )
    
    def _create_financial_summary(self) -> ft.Card:
        """Create financial summary section."""
        if not self.financial_results:
            return ft.Card()
        
        cashflow_data = self.financial_results.get('cashflow')
        if cashflow_data is None:
            return ft.Card()
        
        # Calculate summary metrics
        import pandas as pd
        if isinstance(cashflow_data, dict):
            df = pd.DataFrame(cashflow_data)
        else:
            df = cashflow_data
        
        total_revenue = df['Revenue'].sum() / 1e6
        total_opex = abs(df['OPEX'].sum()) / 1e6
        total_capex = abs(df['Capex'].sum()) / 1e6
        total_grants = df['Grants'].sum() / 1e6
        
        summary_cards = ft.Row([
            self._create_summary_card(
                "Total Revenue",
                f"€{total_revenue:.1f}M",
                ft.Colors.GREEN,
                ft.Icons.TRENDING_UP
            ),
            self._create_summary_card(
                "Total OPEX",
                f"€{total_opex:.1f}M",
                ft.Colors.RED,
                ft.Icons.TRENDING_DOWN
            ),
            self._create_summary_card(
                "Total CAPEX",
                f"€{total_capex:.1f}M",
                ft.Colors.BLUE,
                ft.Icons.BUILD
            ),
            self._create_summary_card(
                "Total Grants",
                f"€{total_grants:.1f}M",
                ft.Colors.PURPLE,
                ft.Icons.CARD_GIFTCARD
            )
        ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
        
        return self.create_card(
            "Project Financial Summary",
            summary_cards,
            icon=ft.Icons.ACCOUNT_BALANCE_WALLET
        )
    
    def _create_summary_card(self, title: str, value: str, color: str, icon: str) -> ft.Container:
        """Create a summary metric card."""
        return ft.Container(
            content=ft.Column([
                ft.Icon(icon, color=color, size=30),
                ft.Text(title, size=12, color=ft.Colors.GREY_600),
                ft.Text(value, size=16, weight=ft.FontWeight.BOLD, color=color)
            ], alignment=ft.MainAxisAlignment.CENTER),
            width=120,
            height=100,
            padding=10,
            bgcolor=f"{color}10",  # Light background
            border_radius=8
        )
    
    def _create_charts_grid(self) -> ft.Container:
        """Create charts grid layout."""
        if not self.financial_results:
            return ft.Container()
        
        # Create chart components
        kpi_gauge_chart = self.kpi_charts.create_kpi_gauge_chart(self.financial_results)
        irr_comparison_chart = self.kpi_charts.create_irr_comparison_chart(self.financial_results)
        cashflow_timeline = self.cashflow_charts.create_cashflow_timeline_chart(self.financial_results)
        dscr_timeline = self.cashflow_charts.create_dscr_timeline_chart(self.financial_results)
        
        charts_grid = ft.Column([
            ft.Row([
                ft.Container(content=kpi_gauge_chart, expand=1),
                ft.Container(content=irr_comparison_chart, expand=1)
            ]),
            ft.Row([
                ft.Container(content=cashflow_timeline, expand=1),
                ft.Container(content=dscr_timeline, expand=1)
            ])
        ])
        
        return self.create_card(
            "Financial Analysis Charts",
            charts_grid,
            icon=ft.Icons.ANALYTICS
        )
    
    def _create_grant_analysis(self) -> ft.Card:
        """Create grant analysis section."""
        assumptions = self.financial_results.get('assumptions', {})
        
        # Calculate grant breakdown
        grant_italy = assumptions.get('grant_meur_italy', 0)
        grant_masen = assumptions.get('grant_meur_masen', 0)
        grant_connection = assumptions.get('grant_meur_connection', 0)
        grant_simest = assumptions.get('grant_meur_simest_africa', 0)
        total_grants = grant_italy + grant_masen + grant_connection + grant_simest
        total_capex = assumptions.get('capex_meur', 1)
        
        grant_percentage = (total_grants / total_capex * 100) if total_capex > 0 else 0
        
        grant_breakdown = ft.Column([
            self.create_info_row("Italian Government Grant", f"€{grant_italy:.2f}M"),
            self.create_info_row("MASEN Strategic Grant", f"€{grant_masen:.2f}M"),
            self.create_info_row("Grid Connection Grant", f"€{grant_connection:.2f}M"),
            self.create_info_row("SIMEST African Fund", f"€{grant_simest:.2f}M"),
            ft.Divider(),
            self.create_info_row("Total Grants", f"€{total_grants:.2f}M", ft.Colors.GREEN),
            self.create_info_row("Grant Percentage", f"{grant_percentage:.1f}%", ft.Colors.GREEN),
        ])
        
        return self.create_card(
            "Grant Analysis",
            grant_breakdown,
            icon=ft.Icons.CARD_GIFTCARD,
            bgcolor=ft.Colors.GREEN_50
        )
    
    def _create_risk_analysis(self) -> ft.Card:
        """Create risk analysis section."""
        if not self.financial_results:
            return ft.Card()
        
        kpis = self.financial_results.get('kpis', {})
        
        # Risk indicators
        risk_indicators = []
        
        # IRR Risk
        project_irr = kpis.get('IRR_project', 0)
        if project_irr < 0.10:
            risk_indicators.append(("High Risk", "Project IRR below 10%", ft.Colors.RED))
        elif project_irr < 0.12:
            risk_indicators.append(("Medium Risk", "Project IRR below target 12%", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Project IRR above target", ft.Colors.GREEN))
        
        # DSCR Risk
        min_dscr = kpis.get('Min_DSCR', 0)
        if min_dscr < 1.20:
            risk_indicators.append(("High Risk", "DSCR below 1.20", ft.Colors.RED))
        elif min_dscr < 1.35:
            risk_indicators.append(("Medium Risk", "DSCR below comfortable level", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Strong debt coverage", ft.Colors.GREEN))
        
        # LCOE Risk
        lcoe = kpis.get('LCOE_eur_kwh', 0)
        if lcoe > 0.055:
            risk_indicators.append(("High Risk", "LCOE above market", ft.Colors.RED))
        elif lcoe > 0.045:
            risk_indicators.append(("Medium Risk", "LCOE above competitive level", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Competitive LCOE", ft.Colors.GREEN))
        
        risk_content = ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.WARNING, color=indicator[2]),
                ft.Text(indicator[0], weight=ft.FontWeight.BOLD, color=indicator[2]),
                ft.Text(indicator[1], expand=True)
            ]) for indicator in risk_indicators
        ])
        
        return self.create_card(
            "Risk Assessment",
            risk_content,
            icon=ft.Icons.SECURITY,
            bgcolor=ft.Colors.ORANGE_50
        )
    
    def update_data(self, data: Dict[str, Any]):
        """Update dashboard with new financial results."""
        if "financial_results" in data:
            self.financial_results = data["financial_results"]
            self.refresh()
    
    def set_financial_results(self, results: Dict[str, Any]):
        """Set financial results for the dashboard."""
        self.financial_results = results
        self.refresh()
    
    def has_data(self) -> bool:
        """Check if dashboard has data to display."""
        return self.financial_results is not None
