"""
Report Generation Service
=========================

Service for generating comprehensive reports and analysis.
"""

from typing import Dict, Any, Optional, Callable, List
import logging
from datetime import datetime
from pathlib import Path

from ..models.client_profile import ClientProfile
from ..models.project_assumptions import EnhancedProjectAssumptions
from .financial_service import FinancialModelService
from .validation_service import ValidationService
from .location_service import LocationComparisonService
from .export_service import ExportService
from ..utils.file_utils import FileUtils


class ReportGenerationService:
    """Service for generating comprehensive reports and analysis."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.financial_service = FinancialModelService()
        self.validation_service = ValidationService()
        self.location_service = LocationComparisonService()
        self.export_service = ExportService()
        self.file_utils = FileUtils()
    
    def generate_comprehensive_report(self,
                                    client_profile: ClientProfile,
                                    assumptions: EnhancedProjectAssumptions,
                                    include_location_comparison: bool = True,
                                    include_sensitivity: bool = True,
                                    include_monte_carlo: bool = True,
                                    include_scenarios: bool = True,
                                    export_formats: List[str] = None,
                                    progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """Generate comprehensive analysis and reports."""
        try:
            if export_formats is None:
                export_formats = ['excel', 'docx', 'html']
            
            if progress_callback:
                progress_callback(5, "Starting comprehensive analysis...")
            
            # Create output directory
            output_dir = self.file_utils.create_timestamped_output_directory(client_profile)
            
            results = {
                'output_directory': output_dir,
                'generated_files': [],
                'analysis_results': {},
                'generation_time': datetime.now().isoformat()
            }
            
            # Step 1: Run financial model
            if progress_callback:
                progress_callback(10, "Running financial model...")
            
            financial_results = self.financial_service.run_financial_model(assumptions)
            results['analysis_results']['financial'] = financial_results
            
            # Step 2: Run validation
            if progress_callback:
                progress_callback(20, "Validating model...")
            
            validation_results = self.validation_service.validate_model(
                assumptions, 
                financial_results['kpis'], 
                financial_results['cashflow']
            )
            results['analysis_results']['validation'] = validation_results
            
            # Step 3: Generate benchmark comparison
            if progress_callback:
                progress_callback(25, "Generating benchmarks...")
            
            benchmark_results = self.validation_service.generate_benchmark_comparison(
                assumptions, 
                financial_results['kpis']
            )
            results['analysis_results']['benchmarks'] = benchmark_results
            
            # Step 4: Location comparison (if requested)
            if include_location_comparison:
                if progress_callback:
                    progress_callback(30, "Running location comparison...")
                
                default_locations = ["Ouarzazate", "Dakhla", "Laâyoune"]
                location_results = self.location_service.compare_locations(
                    assumptions, 
                    default_locations
                )
                results['analysis_results']['location_comparison'] = location_results
            
            # Step 5: Sensitivity analysis (if requested)
            if include_sensitivity:
                if progress_callback:
                    progress_callback(45, "Running sensitivity analysis...")
                
                sensitivity_results = self.financial_service.run_sensitivity_analysis(assumptions)
                results['analysis_results']['sensitivity'] = sensitivity_results
            
            # Step 6: Monte Carlo simulation (if requested)
            if include_monte_carlo:
                if progress_callback:
                    progress_callback(55, "Running Monte Carlo simulation...")
                
                monte_carlo_results = self.financial_service.run_monte_carlo_simulation(
                    assumptions, 
                    n_simulations=1000
                )
                results['analysis_results']['monte_carlo'] = monte_carlo_results
            
            # Step 7: Scenario analysis (if requested)
            if include_scenarios:
                if progress_callback:
                    progress_callback(65, "Running scenario analysis...")
                
                scenario_results = self.financial_service.run_scenario_analysis(assumptions)
                results['analysis_results']['scenarios'] = scenario_results
            
            # Step 8: Generate charts (placeholder for now)
            if progress_callback:
                progress_callback(70, "Generating charts...")
            
            charts = self._generate_charts(results['analysis_results'])
            results['charts'] = charts
            
            # Step 9: Export reports
            export_progress_start = 75
            export_progress_range = 25
            
            for i, format_type in enumerate(export_formats):
                format_progress = export_progress_start + (i / len(export_formats)) * export_progress_range
                
                if format_type.lower() == 'excel':
                    if progress_callback:
                        progress_callback(format_progress, "Exporting Excel report...")
                    
                    excel_file = self.export_service.export_excel_report(
                        client_profile=client_profile,
                        assumptions=assumptions,
                        financial_results=financial_results,
                        sensitivity_results=results['analysis_results'].get('sensitivity'),
                        monte_carlo_results=results['analysis_results'].get('monte_carlo'),
                        scenario_results=results['analysis_results'].get('scenarios'),
                        output_dir=output_dir
                    )
                    results['generated_files'].append(('Excel Report', excel_file))
                
                elif format_type.lower() == 'docx':
                    if progress_callback:
                        progress_callback(format_progress, "Exporting DOCX report...")
                    
                    docx_file = self.export_service.export_docx_report(
                        client_profile=client_profile,
                        assumptions=assumptions,
                        financial_results=financial_results,
                        validation_results=validation_results,
                        charts=charts,
                        output_dir=output_dir
                    )
                    results['generated_files'].append(('DOCX Report', docx_file))
                
                elif format_type.lower() == 'html':
                    if progress_callback:
                        progress_callback(format_progress, "Exporting HTML report...")
                    
                    html_file = self.export_service.export_html_report(
                        client_profile=client_profile,
                        assumptions=assumptions,
                        financial_results=financial_results,
                        output_dir=output_dir
                    )
                    results['generated_files'].append(('HTML Report', html_file))
            
            # Step 10: Export raw data
            if progress_callback:
                progress_callback(95, "Exporting raw data...")
            
            json_file = self.export_service.export_json_data(
                client_profile=client_profile,
                assumptions=assumptions,
                financial_results=financial_results,
                output_dir=output_dir
            )
            results['generated_files'].append(('Raw Data (JSON)', json_file))
            
            # Step 11: Generate summary
            if progress_callback:
                progress_callback(98, "Generating summary...")
            
            results['summary'] = self._generate_analysis_summary(results['analysis_results'])
            
            if progress_callback:
                progress_callback(100, "Comprehensive analysis completed!")
            
            self.logger.info(f"Comprehensive report generated with {len(results['generated_files'])} files")
            return results
            
        except Exception as e:
            self.logger.error(f"Error generating comprehensive report: {str(e)}")
            raise
    
    def _generate_charts(self, analysis_results: Dict[str, Any]) -> Dict[str, bytes]:
        """Generate charts for the analysis (placeholder implementation)."""
        # This is a placeholder - in a real implementation, you would generate
        # actual chart images using matplotlib, plotly, etc.
        charts = {}
        
        # For now, return empty dict - charts would be generated by chart components
        self.logger.info("Chart generation placeholder - charts would be generated by chart components")
        
        return charts
    
    def _generate_analysis_summary(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of all analysis results."""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'analyses_performed': list(analysis_results.keys()),
            'key_findings': {},
            'recommendations': [],
            'risk_factors': [],
            'opportunities': []
        }
        
        # Financial summary
        if 'financial' in analysis_results:
            financial = analysis_results['financial']
            kpis = financial.get('kpis', {})
            
            summary['key_findings']['financial'] = {
                'project_irr': kpis.get('IRR_project', 0),
                'equity_irr': kpis.get('IRR_equity', 0),
                'npv_project_meur': kpis.get('NPV_project', 0) / 1e6,
                'lcoe_eur_kwh': kpis.get('LCOE_eur_kwh', 0),
                'min_dscr': kpis.get('Min_DSCR', 0)
            }
            
            # Generate recommendations based on KPIs
            if kpis.get('IRR_project', 0) > 0.12:
                summary['opportunities'].append("Strong project returns above 12% threshold")
            else:
                summary['risk_factors'].append("Project IRR below 12% target")
            
            if kpis.get('Min_DSCR', 0) < 1.25:
                summary['risk_factors'].append("DSCR below comfortable threshold")
            
            if kpis.get('LCOE_eur_kwh', 1) < 0.045:
                summary['opportunities'].append("Competitive LCOE below 4.5 c€/kWh")
        
        # Validation summary
        if 'validation' in analysis_results:
            validation = analysis_results['validation']
            summary['key_findings']['validation'] = {
                'is_valid': validation.is_valid,
                'warning_count': len(validation.warnings) if hasattr(validation, 'warnings') else 0,
                'error_count': len(validation.errors) if hasattr(validation, 'errors') else 0
            }
            
            if not validation.is_valid:
                summary['risk_factors'].append("Model validation failed - review required")
        
        # Location comparison summary
        if 'location_comparison' in analysis_results:
            location_comp = analysis_results['location_comparison']
            analysis = location_comp.get('analysis', {})
            recommendations = analysis.get('recommendations', {})
            
            if 'best_overall' in recommendations:
                best_location = recommendations['best_overall']['location']
                summary['key_findings']['best_location'] = best_location
                summary['recommendations'].append(f"Consider {best_location} as optimal location")
        
        # Sensitivity analysis summary
        if 'sensitivity' in analysis_results:
            summary['key_findings']['sensitivity_completed'] = True
            summary['recommendations'].append("Review sensitivity analysis for key risk factors")
        
        # Monte Carlo summary
        if 'monte_carlo' in analysis_results:
            mc_results = analysis_results['monte_carlo']
            if 'statistics' in mc_results:
                summary['key_findings']['monte_carlo'] = {
                    'simulations_run': mc_results.get('n_simulations', 0),
                    'analysis_completed': True
                }
                summary['recommendations'].append("Review Monte Carlo results for risk assessment")
        
        # Generate overall recommendation
        if len(summary['risk_factors']) == 0:
            summary['overall_assessment'] = "Project shows strong fundamentals with minimal risks"
        elif len(summary['risk_factors']) <= 2:
            summary['overall_assessment'] = "Project viable with some risks to monitor"
        else:
            summary['overall_assessment'] = "Project requires careful review due to multiple risk factors"
        
        return summary
    
    def generate_executive_summary(self, analysis_results: Dict[str, Any]) -> str:
        """Generate executive summary text."""
        summary = self._generate_analysis_summary(analysis_results)
        
        executive_summary = f"""
EXECUTIVE SUMMARY
================

Analysis Date: {datetime.now().strftime('%Y-%m-%d')}

OVERALL ASSESSMENT: {summary.get('overall_assessment', 'Analysis completed')}

KEY FINANCIAL METRICS:
"""
        
        if 'financial' in summary['key_findings']:
            financial = summary['key_findings']['financial']
            executive_summary += f"""
- Project IRR: {financial.get('project_irr', 0):.1%}
- Equity IRR: {financial.get('equity_irr', 0):.1%}
- NPV Project: €{financial.get('npv_project_meur', 0):.1f}M
- LCOE: {financial.get('lcoe_eur_kwh', 0):.3f} €/kWh
- Minimum DSCR: {financial.get('min_dscr', 0):.2f}
"""
        
        if summary['opportunities']:
            executive_summary += f"\nOPPORTUNITIES:\n"
            for opportunity in summary['opportunities']:
                executive_summary += f"• {opportunity}\n"
        
        if summary['risk_factors']:
            executive_summary += f"\nRISK FACTORS:\n"
            for risk in summary['risk_factors']:
                executive_summary += f"• {risk}\n"
        
        if summary['recommendations']:
            executive_summary += f"\nRECOMMENDATIONS:\n"
            for recommendation in summary['recommendations']:
                executive_summary += f"• {recommendation}\n"
        
        return executive_summary
