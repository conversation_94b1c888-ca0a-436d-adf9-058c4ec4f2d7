"""
Location Comparison Service
===========================

Service for location comparison and analysis.
"""

from typing import Dict, Any, List, Optional, Callable
import pandas as pd
import logging

from ..models.location_config import LocationManager, LocationConfig
from ..models.project_assumptions import EnhancedProjectAssumptions
from .financial_service import FinancialModelService


class LocationComparisonService:
    """Service for comparing different project locations."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.location_manager = LocationManager()
        self.financial_service = FinancialModelService()
        self._comparison_results: Optional[Dict[str, Any]] = None
    
    def compare_locations(self,
                         base_assumptions: EnhancedProjectAssumptions,
                         location_names: List[str],
                         progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """Compare multiple locations for the project."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up location comparison...")
            
            comparison_results = {}
            total_locations = len(location_names)
            
            for i, location_name in enumerate(location_names):
                if progress_callback:
                    progress = 20 + (i / total_locations) * 60
                    progress_callback(progress, f"Analyzing {location_name}...")
                
                # Get location configuration
                location_config = self.location_manager.get_location(location_name)
                if not location_config:
                    self.logger.warning(f"Location {location_name} not found, skipping")
                    continue
                
                # Create modified assumptions for this location
                location_assumptions = self._create_location_assumptions(base_assumptions, location_config)
                
                # Run financial model for this location
                try:
                    financial_results = self.financial_service.run_financial_model(location_assumptions)
                    
                    # Store results
                    comparison_results[location_name] = {
                        'location_config': location_config.to_dict(),
                        'assumptions': location_assumptions.to_dict(),
                        'financial_results': financial_results,
                        'kpis': financial_results['kpis'],
                        'summary': self._create_location_summary(location_config, financial_results)
                    }
                    
                except Exception as e:
                    self.logger.error(f"Error analyzing location {location_name}: {str(e)}")
                    comparison_results[location_name] = {
                        'error': str(e),
                        'location_config': location_config.to_dict()
                    }
            
            if progress_callback:
                progress_callback(90, "Generating comparison analysis...")
            
            # Generate comparison analysis
            comparison_analysis = self._generate_comparison_analysis(comparison_results)
            
            self._comparison_results = {
                'locations': comparison_results,
                'analysis': comparison_analysis,
                'base_assumptions': base_assumptions.to_dict(),
                'comparison_date': pd.Timestamp.now().isoformat()
            }
            
            if progress_callback:
                progress_callback(100, "Location comparison completed")
            
            self.logger.info(f"Location comparison completed for {len(location_names)} locations")
            return self._comparison_results
            
        except Exception as e:
            self.logger.error(f"Error in location comparison: {str(e)}")
            raise
    
    def _create_location_assumptions(self, 
                                   base_assumptions: EnhancedProjectAssumptions,
                                   location_config: LocationConfig) -> EnhancedProjectAssumptions:
        """Create modified assumptions for a specific location."""
        # Create a copy of base assumptions
        location_assumptions = base_assumptions.copy_with_modifications(
            location_name=location_config.name,
            production_mwh_year1=location_config.production_mwh_year1,
            capex_meur=location_config.capex_meur,
            opex_keuros_year1=location_config.opex_keuros_year1,
            ppa_price_eur_kwh=location_config.ppa_price_eur_kwh,
            land_lease_eur_mw_year=location_config.land_lease_eur_mw_year
        )
        
        return location_assumptions
    
    def _create_location_summary(self, 
                               location_config: LocationConfig,
                               financial_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create summary for a location."""
        kpis = financial_results.get('kpis', {})
        
        return {
            'location_name': location_config.name,
            'description': location_config.description,
            'irr_project': kpis.get('IRR_project', 0),
            'irr_equity': kpis.get('IRR_equity', 0),
            'npv_project_meur': kpis.get('NPV_project', 0) / 1e6,
            'npv_equity_meur': kpis.get('NPV_equity', 0) / 1e6,
            'lcoe_eur_kwh': kpis.get('LCOE_eur_kwh', 0),
            'min_dscr': kpis.get('Min_DSCR', 0),
            'capacity_factor': location_config.production_mwh_year1 / (8760 * financial_results['assumptions']['capacity_mw']),
            'specific_capex_eur_kw': location_config.capex_meur * 1e6 / (financial_results['assumptions']['capacity_mw'] * 1000),
            'advantages': location_config.advantages,
            'challenges': location_config.challenges,
            'irradiation_kwh_m2': location_config.irradiation_kwh_m2
        }
    
    def _generate_comparison_analysis(self, comparison_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive comparison analysis."""
        analysis = {}
        
        # Extract valid results (no errors)
        valid_results = {name: data for name, data in comparison_results.items() 
                        if 'error' not in data}
        
        if not valid_results:
            return {'error': 'No valid location results to compare'}
        
        # Create comparison DataFrame
        comparison_data = []
        for location_name, data in valid_results.items():
            summary = data['summary']
            comparison_data.append({
                'Location': location_name,
                'IRR_Project': summary['irr_project'],
                'IRR_Equity': summary['irr_equity'],
                'NPV_Project_MEUR': summary['npv_project_meur'],
                'NPV_Equity_MEUR': summary['npv_equity_meur'],
                'LCOE_EUR_kWh': summary['lcoe_eur_kwh'],
                'Min_DSCR': summary['min_dscr'],
                'Capacity_Factor': summary['capacity_factor'],
                'Specific_CAPEX_EUR_kW': summary['specific_capex_eur_kw'],
                'Irradiation_kWh_m2': summary['irradiation_kwh_m2']
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        
        # Rankings
        analysis['rankings'] = {
            'best_irr_project': self._get_ranking(comparison_df, 'IRR_Project', ascending=False),
            'best_irr_equity': self._get_ranking(comparison_df, 'IRR_Equity', ascending=False),
            'best_npv_project': self._get_ranking(comparison_df, 'NPV_Project_MEUR', ascending=False),
            'best_npv_equity': self._get_ranking(comparison_df, 'NPV_Equity_MEUR', ascending=False),
            'lowest_lcoe': self._get_ranking(comparison_df, 'LCOE_EUR_kWh', ascending=True),
            'highest_dscr': self._get_ranking(comparison_df, 'Min_DSCR', ascending=False),
            'highest_capacity_factor': self._get_ranking(comparison_df, 'Capacity_Factor', ascending=False),
            'lowest_capex': self._get_ranking(comparison_df, 'Specific_CAPEX_EUR_kW', ascending=True)
        }
        
        # Statistics
        analysis['statistics'] = {
            'irr_project': {
                'mean': comparison_df['IRR_Project'].mean(),
                'std': comparison_df['IRR_Project'].std(),
                'min': comparison_df['IRR_Project'].min(),
                'max': comparison_df['IRR_Project'].max()
            },
            'irr_equity': {
                'mean': comparison_df['IRR_Equity'].mean(),
                'std': comparison_df['IRR_Equity'].std(),
                'min': comparison_df['IRR_Equity'].min(),
                'max': comparison_df['IRR_Equity'].max()
            },
            'lcoe': {
                'mean': comparison_df['LCOE_EUR_kWh'].mean(),
                'std': comparison_df['LCOE_EUR_kWh'].std(),
                'min': comparison_df['LCOE_EUR_kWh'].min(),
                'max': comparison_df['LCOE_EUR_kWh'].max()
            }
        }
        
        # Recommendations
        analysis['recommendations'] = self._generate_recommendations(comparison_df, valid_results)
        
        # Comparison matrix
        analysis['comparison_matrix'] = comparison_df.to_dict('records')
        
        return analysis
    
    def _get_ranking(self, df: pd.DataFrame, column: str, ascending: bool = False) -> List[Dict[str, Any]]:
        """Get ranking for a specific metric."""
        sorted_df = df.sort_values(column, ascending=ascending)
        ranking = []
        
        for i, (_, row) in enumerate(sorted_df.iterrows()):
            ranking.append({
                'rank': i + 1,
                'location': row['Location'],
                'value': row[column]
            })
        
        return ranking
    
    def _generate_recommendations(self, 
                                comparison_df: pd.DataFrame,
                                valid_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate location recommendations."""
        recommendations = {}
        
        # Best overall location (weighted score)
        weights = {
            'IRR_Project': 0.25,
            'IRR_Equity': 0.25,
            'LCOE_EUR_kWh': -0.20,  # Lower is better
            'Min_DSCR': 0.15,
            'Capacity_Factor': 0.15
        }
        
        # Normalize metrics for scoring
        normalized_df = comparison_df.copy()
        for metric in weights.keys():
            if metric == 'LCOE_EUR_kWh':  # Lower is better
                normalized_df[f'{metric}_norm'] = 1 - (normalized_df[metric] - normalized_df[metric].min()) / (normalized_df[metric].max() - normalized_df[metric].min())
            else:  # Higher is better
                normalized_df[f'{metric}_norm'] = (normalized_df[metric] - normalized_df[metric].min()) / (normalized_df[metric].max() - normalized_df[metric].min())
        
        # Calculate weighted scores
        normalized_df['weighted_score'] = 0
        for metric, weight in weights.items():
            normalized_df['weighted_score'] += normalized_df[f'{metric}_norm'] * abs(weight)
        
        # Best overall location
        best_overall = normalized_df.loc[normalized_df['weighted_score'].idxmax()]
        recommendations['best_overall'] = {
            'location': best_overall['Location'],
            'score': best_overall['weighted_score'],
            'reasons': self._get_location_strengths(best_overall['Location'], valid_results)
        }
        
        # Best for different criteria
        recommendations['best_for_returns'] = comparison_df.loc[comparison_df['IRR_Equity'].idxmax()]['Location']
        recommendations['best_for_lcoe'] = comparison_df.loc[comparison_df['LCOE_EUR_kWh'].idxmin()]['Location']
        recommendations['best_for_risk'] = comparison_df.loc[comparison_df['Min_DSCR'].idxmax()]['Location']
        
        # Risk assessment
        recommendations['risk_assessment'] = self._assess_location_risks(valid_results)
        
        return recommendations
    
    def _get_location_strengths(self, location_name: str, valid_results: Dict[str, Any]) -> List[str]:
        """Get strengths of a specific location."""
        location_data = valid_results.get(location_name, {})
        summary = location_data.get('summary', {})
        
        strengths = []
        
        if summary.get('irr_project', 0) > 0.12:
            strengths.append("Strong project returns")
        
        if summary.get('lcoe_eur_kwh', 1) < 0.045:
            strengths.append("Competitive LCOE")
        
        if summary.get('capacity_factor', 0) > 0.25:
            strengths.append("High capacity factor")
        
        if summary.get('min_dscr', 0) > 1.35:
            strengths.append("Strong debt service coverage")
        
        # Add location-specific advantages
        advantages = summary.get('advantages', [])
        strengths.extend(advantages[:2])  # Add top 2 advantages
        
        return strengths
    
    def _assess_location_risks(self, valid_results: Dict[str, Any]) -> Dict[str, List[str]]:
        """Assess risks for each location."""
        risk_assessment = {}
        
        for location_name, data in valid_results.items():
            summary = data['summary']
            risks = []
            
            if summary.get('irr_project', 0) < 0.10:
                risks.append("Low project returns")
            
            if summary.get('min_dscr', 0) < 1.25:
                risks.append("Tight debt service coverage")
            
            if summary.get('capacity_factor', 0) < 0.20:
                risks.append("Low capacity factor")
            
            # Add location-specific challenges
            challenges = summary.get('challenges', [])
            risks.extend(challenges[:2])  # Add top 2 challenges
            
            risk_assessment[location_name] = risks
        
        return risk_assessment
    
    def get_comparison_results(self) -> Optional[Dict[str, Any]]:
        """Get current comparison results."""
        return self._comparison_results
    
    def has_comparison_results(self) -> bool:
        """Check if comparison has been performed."""
        return self._comparison_results is not None
    
    def clear_results(self):
        """Clear comparison results."""
        self._comparison_results = None
        self.logger.info("Location comparison results cleared")
    
    def get_available_locations(self) -> List[str]:
        """Get list of available locations."""
        return self.location_manager.get_location_names()
    
    def add_custom_location(self, location_config: LocationConfig):
        """Add a custom location configuration."""
        self.location_manager.add_location(location_config)
        self.logger.info(f"Added custom location: {location_config.name}")
    
    def export_comparison_results(self) -> Optional[pd.DataFrame]:
        """Export comparison results as DataFrame."""
        if not self._comparison_results:
            return None
        
        analysis = self._comparison_results.get('analysis', {})
        comparison_matrix = analysis.get('comparison_matrix', [])
        
        if comparison_matrix:
            return pd.DataFrame(comparison_matrix)
        
        return None
