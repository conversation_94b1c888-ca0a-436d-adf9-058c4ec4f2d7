"""
Integration Tests
=================

Integration tests for the complete application workflow.
"""

import unittest
from unittest.mock import patch, MagicMock
import tempfile
import shutil
from pathlib import Path

from ..models.client_profile import ClientProfile
from ..models.project_assumptions import EnhancedProjectAssumptions
from ..services.financial_service import FinancialModelService
from ..services.validation_service import ValidationService
from ..services.export_service import ExportService
from ..app.app_state import AppState


class TestCompleteWorkflow(unittest.TestCase):
    """Test complete application workflow."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
        
        # Create test client profile
        self.client_profile = ClientProfile()
        self.client_profile.company_name = "Test Solar Company"
        self.client_profile.client_name = "<PERSON>"
        self.client_profile.project_name = "Test Solar Project"
        self.client_profile.contact_email = "<EMAIL>"
        self.client_profile.project_capacity_mw = 10.0
        
        # Create test project assumptions
        self.assumptions = EnhancedProjectAssumptions()
        self.assumptions.capacity_mw = 10.0
        self.assumptions.production_mwh_year1 = 18000.0
        self.assumptions.capex_meur = 8.5
        self.assumptions.opex_keuros_year1 = 180.0
        self.assumptions.ppa_price_eur_kwh = 0.045
        self.assumptions.debt_ratio = 0.75
        self.assumptions.interest_rate = 0.06
        self.assumptions.discount_rate = 0.08
        
        # Initialize services
        self.financial_service = FinancialModelService()
        self.validation_service = ValidationService()
        self.export_service = ExportService()
    
    def test_client_profile_validation_workflow(self):
        """Test client profile validation workflow."""
        # Test incomplete profile
        incomplete_profile = ClientProfile()
        errors = incomplete_profile.validate()
        self.assertGreater(len(errors), 0)
        self.assertFalse(incomplete_profile.is_complete())
        
        # Test complete profile
        errors = self.client_profile.validate()
        self.assertEqual(len(errors), 0)
        self.assertTrue(self.client_profile.is_complete())
    
    def test_project_assumptions_validation_workflow(self):
        """Test project assumptions validation workflow."""
        # Test valid assumptions
        errors = self.assumptions.validate_all()
        self.assertEqual(len(errors), 0)
        self.assertTrue(self.assumptions.is_validated)
        
        # Test invalid assumptions
        invalid_assumptions = EnhancedProjectAssumptions()
        invalid_assumptions.capacity_mw = -5.0  # Invalid
        invalid_assumptions.debt_ratio = 1.5    # Invalid
        
        errors = invalid_assumptions.validate_all()
        self.assertGreater(len(errors), 0)
        self.assertFalse(invalid_assumptions.is_validated)
    
    @patch('src.new_app.services.financial_service.run_enhanced_financial_model')
    def test_financial_model_workflow(self, mock_run_model):
        """Test complete financial modeling workflow."""
        # Mock financial model results
        mock_results = {
            'kpis': {
                'IRR_project': 0.12,
                'IRR_equity': 0.15,
                'NPV_project': 5000000,
                'NPV_equity': 2000000,
                'LCOE_eur_kwh': 0.042,
                'Min_DSCR': 1.35,
                'Avg_DSCR': 1.45,
                'Payback_years': 8.5
            },
            'cashflow': {
                'Year': [1, 2, 3, 4, 5],
                'Revenue': [810000, 810000, 810000, 810000, 810000],
                'OPEX': [-180000, -180000, -180000, -180000, -180000],
                'Capex': [-8500000, 0, 0, 0, 0],
                'Equity_CF': [-2125000, 500000, 520000, 540000, 560000],
                'DSCR': [0, 1.4, 1.3, 1.5, 1.6]
            },
            'assumptions': self.assumptions.to_dict()
        }
        mock_run_model.return_value = mock_results
        
        # Run financial model
        results = self.financial_service.run_financial_model(self.assumptions)
        
        # Verify results structure
        self.assertIsNotNone(results)
        self.assertIn('kpis', results)
        self.assertIn('cashflow', results)
        self.assertIn('assumptions', results)
        
        # Verify KPIs
        kpis = results['kpis']
        self.assertGreater(kpis['IRR_project'], 0)
        self.assertGreater(kpis['IRR_equity'], 0)
        self.assertGreater(kpis['NPV_project'], 0)
        self.assertLess(kpis['LCOE_eur_kwh'], 0.1)  # Reasonable LCOE
        self.assertGreater(kpis['Min_DSCR'], 1.0)   # DSCR above 1
        
        mock_run_model.assert_called_once()
    
    @patch('src.new_app.services.financial_service.run_enhanced_financial_model')
    def test_validation_workflow(self, mock_run_model):
        """Test model validation workflow."""
        # Mock financial results
        mock_results = {
            'kpis': {
                'IRR_project': 0.12,
                'IRR_equity': 0.15,
                'NPV_project': 5000000,
                'LCOE_eur_kwh': 0.042,
                'Min_DSCR': 1.35
            },
            'cashflow': {
                'Year': [1, 2, 3],
                'DSCR': [1.4, 1.3, 1.5]
            }
        }
        mock_run_model.return_value = mock_results
        
        # Run financial model
        financial_results = self.financial_service.run_financial_model(self.assumptions)
        
        # Run validation
        validation_results = self.validation_service.validate_model(
            self.assumptions,
            financial_results['kpis'],
            financial_results['cashflow']
        )
        
        # Verify validation results
        self.assertIsNotNone(validation_results)
        self.assertTrue(validation_results.is_valid)
        self.assertIsInstance(validation_results.warnings, list)
        self.assertIsInstance(validation_results.errors, list)
        
        # Generate benchmark comparison
        benchmark_results = self.validation_service.generate_benchmark_comparison(
            self.assumptions,
            financial_results['kpis']
        )
        
        self.assertIsNotNone(benchmark_results)
        self.assertIn('custom_analysis', benchmark_results)
    
    @patch('src.new_app.services.financial_service.run_enhanced_financial_model')
    @patch('src.new_app.utils.file_utils.FileUtils.create_timestamped_output_directory')
    def test_export_workflow(self, mock_create_dir, mock_run_model):
        """Test export workflow."""
        # Mock directory creation
        mock_create_dir.return_value = {
            'main_dir': Path(self.temp_dir),
            'data_dir': Path(self.temp_dir) / 'data',
            'charts_dir': Path(self.temp_dir) / 'charts',
            'reports_dir': Path(self.temp_dir) / 'reports'
        }
        
        # Create actual directories for testing
        for dir_path in mock_create_dir.return_value.values():
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Mock financial results
        mock_results = {
            'kpis': {
                'IRR_project': 0.12,
                'IRR_equity': 0.15,
                'NPV_project': 5000000
            },
            'cashflow': {
                'Year': [1, 2, 3],
                'Revenue': [810000, 810000, 810000]
            }
        }
        mock_run_model.return_value = mock_results
        
        # Run financial model
        financial_results = self.financial_service.run_financial_model(self.assumptions)
        
        # Test JSON export
        with patch('builtins.open', create=True) as mock_open:
            with patch('json.dump') as mock_json_dump:
                json_file = self.export_service.export_json_data(
                    self.client_profile,
                    self.assumptions,
                    financial_results
                )
                
                self.assertIsNotNone(json_file)
                mock_open.assert_called()
                mock_json_dump.assert_called()
    
    def test_app_state_workflow(self):
        """Test application state management workflow."""
        # Initialize app state
        app_state = AppState()
        
        # Set client profile and assumptions
        app_state.client_profile = self.client_profile
        app_state.project_assumptions = self.assumptions
        
        # Verify state
        self.assertEqual(app_state.client_profile.company_name, "Test Solar Company")
        self.assertEqual(app_state.project_assumptions.capacity_mw, 10.0)
        
        # Test state validation
        issues = app_state.validate_state()
        self.assertEqual(len(issues), 0)  # Should be valid
        
        # Test state serialization
        state_dict = app_state.to_dict()
        self.assertIn('client_profile', state_dict)
        self.assertIn('project_assumptions', state_dict)
        self.assertIn('metadata', state_dict)
        
        # Test state loading
        new_app_state = AppState()
        new_app_state.load_from_dict(state_dict)
        
        self.assertEqual(new_app_state.client_profile.company_name, "Test Solar Company")
        self.assertEqual(new_app_state.project_assumptions.capacity_mw, 10.0)
    
    @patch('src.new_app.services.financial_service.run_enhanced_financial_model')
    def test_complete_analysis_workflow(self, mock_run_model):
        """Test complete analysis workflow from start to finish."""
        # Mock financial model
        mock_results = {
            'kpis': {
                'IRR_project': 0.12,
                'IRR_equity': 0.15,
                'NPV_project': 5000000,
                'LCOE_eur_kwh': 0.042,
                'Min_DSCR': 1.35
            },
            'cashflow': {
                'Year': list(range(1, 26)),
                'Revenue': [810000] * 25,
                'OPEX': [-180000] * 25,
                'DSCR': [1.4] * 25
            }
        }
        mock_run_model.return_value = mock_results
        
        # Step 1: Validate inputs
        client_errors = self.client_profile.validate()
        assumption_errors = self.assumptions.validate_all()
        
        self.assertEqual(len(client_errors), 0)
        self.assertEqual(len(assumption_errors), 0)
        
        # Step 2: Run financial model
        financial_results = self.financial_service.run_financial_model(self.assumptions)
        self.assertIsNotNone(financial_results)
        
        # Step 3: Validate model results
        validation_results = self.validation_service.validate_model(
            self.assumptions,
            financial_results['kpis'],
            financial_results['cashflow']
        )
        self.assertTrue(validation_results.is_valid)
        
        # Step 4: Generate benchmark comparison
        benchmark_results = self.validation_service.generate_benchmark_comparison(
            self.assumptions,
            financial_results['kpis']
        )
        self.assertIsNotNone(benchmark_results)
        
        # Step 5: Test sensitivity analysis
        with patch.object(self.financial_service, '_run_sensitivity_analysis') as mock_sensitivity:
            mock_sensitivity.return_value = MagicMock()
            
            sensitivity_results = self.financial_service.run_sensitivity_analysis(
                self.assumptions,
                ['production_mwh_year1', 'ppa_price_eur_kwh']
            )
            self.assertIsNotNone(sensitivity_results)
        
        # Step 6: Test Monte Carlo simulation
        with patch.object(self.financial_service, '_run_monte_carlo_simulation') as mock_mc:
            mock_mc.return_value = {
                'statistics': {'mean_irr': 0.12},
                'n_simulations': 1000
            }
            
            mc_results = self.financial_service.run_monte_carlo_simulation(
                self.assumptions,
                1000
            )
            self.assertIsNotNone(mc_results)
        
        # Verify complete workflow success
        self.assertTrue(True)  # If we get here, the workflow completed successfully


if __name__ == '__main__':
    unittest.main()
